import MetaTrader5 as mt5 # type: ignore
import logging
import time
from datetime import datetime, timedelta
from pytz import timezone
import pandas as pd
from abc import ABC
from typing import Dict, List, Any, Optional, Tuple
import threading

log = logging.getLogger("ZoneBoS.BaseTrader")

class BaseTrader:
    """Base class for trading operations with common functionality.

    This class serves as the central broker abstraction layer, providing
    a unified interface for all trading operations regardless of the underlying
    broker platform (MT5, etc.). All broker-specific functionality is contained
    within this class to enable easy broker switching in the future.
    """

    def __init__(self, config, strategy_instance):
        self.cfg = config

        # Validate strategy instance implements required interface
        self._validate_strategy_interface(strategy_instance)
        self.strategy = strategy_instance

        self._prev_tick_price = None

        # Common trading attributes
        self.current_martingale_sequence_id = 0
        self.trade_history = []
        self.pause_trading_until = None
        self.stop_trading_until = None
        self.daily_profit = {}
        self.active_position_slots = {}
        self.available_position_slots = list(range(1, self.cfg.MAX_OPEN_POSITIONS + 1))

        # Performance optimization: cache position count
        self._cached_open_positions_count = 0
        self._position_count_lock = threading.Lock()

        # Trade history management
        self.max_trade_history_size = getattr(config, 'MAX_TRADE_HISTORY_SIZE', 10000)
        self.archived_trades = []

        # Tick monitoring
        self.tick_history = []
        self.tick_stats = {
            'total_ticks': 0,
            'last_tick_time': None,
            'last_stats_time': None,
            'price_changes': [],
            'spread_history': [],
            'tick_frequency': 0.0
        }
        self.last_tick_monitoring_time = None
        
        # Strategy information display tracking
        self.last_strategy_info = None
        self.last_strategy_info_time = None
        
        # Signal logging tracking to prevent spam
        self.last_signal_time = None
        self.last_signal_info = None

        # Broker abstraction - timeframe constants
        self.TIMEFRAME_M1 = mt5.TIMEFRAME_M1
        self.TIMEFRAME_M5 = mt5.TIMEFRAME_M5
        self.TIMEFRAME_M15 = mt5.TIMEFRAME_M15
        self.TIMEFRAME_M30 = mt5.TIMEFRAME_M30
        self.TIMEFRAME_H1 = mt5.TIMEFRAME_H1
        self.TIMEFRAME_H4 = mt5.TIMEFRAME_H4
        self.TIMEFRAME_D1 = mt5.TIMEFRAME_D1

        # Timeframe mapping for string to MT5 constant conversion
        self.timeframe_map = {
            "M1": self.TIMEFRAME_M1,
            "M5": self.TIMEFRAME_M5,
            "M15": self.TIMEFRAME_M15,
            "M30": self.TIMEFRAME_M30,
            "H1": self.TIMEFRAME_H1,
            "H4": self.TIMEFRAME_H4,
            "D1": self.TIMEFRAME_D1
        }

        # Get configured timeframe
        self.configured_timeframe = self._get_configured_timeframe()

    def _validate_strategy_interface(self, strategy_instance):
        """Validate that strategy instance implements required interface methods."""
        required_methods = [
            'initialize', 'update', 'get_entry_signals', 'get_strategy_state',
            'get_position_size', 'get_stop_loss', 'get_take_profit', 'on_trade_opened'
        ]

        missing_methods = []
        for method_name in required_methods:
            if not hasattr(strategy_instance, method_name):
                missing_methods.append(method_name)
            elif not callable(getattr(strategy_instance, method_name)):
                missing_methods.append(f"{method_name} (not callable)")

        if missing_methods:
            raise ValueError(f"Strategy instance missing required methods: {missing_methods}")

        log.info(f"Strategy interface validation passed for {strategy_instance.__class__.__name__}")

    def _get_consistent_timezone(self):
        """Get consistent timezone object for all operations."""
        try:
            return timezone(self.cfg.TRADING_TIMEZONE)
        except Exception as e:
            log.warning(f"Invalid timezone {self.cfg.TRADING_TIMEZONE}: {e}. Using UTC.")
            return timezone('UTC')

    def _get_current_time(self):
        """Get current time in consistent timezone."""
        return datetime.now(self._get_consistent_timezone())

    def _get_configured_timeframe(self):
        """Get the MT5 timeframe constant from the configured timeframe string."""
        timeframe_str = getattr(self.cfg, 'TIMEFRAME', 'M1')
        return self.timeframe_map.get(timeframe_str, self.TIMEFRAME_M1)

    # ═══════════════════════════════════════════════════════════════════════════════
    # TICK MONITORING SYSTEM
    # ═══════════════════════════════════════════════════════════════════════════════

    def _update_tick_monitoring(self, tick_data, current_time):
        """Update tick monitoring statistics and history."""
        if not getattr(self.cfg, 'ENABLE_TICK_MONITORING', True):
            return

        # Add tick to history
        tick_info = {
            'time': current_time,
            'bid': tick_data['bid'],
            'ask': tick_data['ask'],
            'spread': tick_data['ask'] - tick_data['bid'],
            'mid_price': (tick_data['ask'] + tick_data['bid']) / 2
        }

        self.tick_history.append(tick_info)

        # Maintain tick history size
        max_size = getattr(self.cfg, 'TICK_HISTORY_SIZE', 1000)
        if len(self.tick_history) > max_size:
            self.tick_history = self.tick_history[-max_size:]

        # Update statistics
        self.tick_stats['total_ticks'] += 1
        self.tick_stats['last_tick_time'] = current_time

        # Calculate price changes
        if len(self.tick_history) > 1:
            prev_tick = self.tick_history[-2]
            price_change = tick_info['mid_price'] - prev_tick['mid_price']
            self.tick_stats['price_changes'].append(price_change)

            # Keep only recent price changes
            if len(self.tick_stats['price_changes']) > 100:
                self.tick_stats['price_changes'] = self.tick_stats['price_changes'][-100:]

        # Update spread history
        self.tick_stats['spread_history'].append(tick_info['spread'])
        if len(self.tick_stats['spread_history']) > 100:
            self.tick_stats['spread_history'] = self.tick_stats['spread_history'][-100:]

        # Calculate tick frequency
        if len(self.tick_history) >= 2:
            time_diff = (self.tick_history[-1]['time'] - self.tick_history[-2]['time']).total_seconds()
            if time_diff > 0:
                self.tick_stats['tick_frequency'] = 1.0 / time_diff

    def _log_tick_statistics(self, current_time):
        """Log strategy information periodically and only when meaningful changes occur."""
        if not hasattr(self, 'strategy'):
            return

        info_interval = getattr(self.cfg, 'STRATEGY_INFO_INTERVAL', 1800)  # 30 minutes default

        # Check if enough time has passed since last update
        time_to_update = (self.last_strategy_info_time is None or
                         (current_time - self.last_strategy_info_time).total_seconds() >= info_interval)

        if time_to_update:
            self._log_strategy_information_if_changed(current_time)

    def _log_strategy_information_if_changed(self, current_time):
        """Log strategy information only if it has meaningfully changed."""
        try:
            # Get current price
            current_price = 0.0
            if len(self.tick_history) > 0:
                current_price = self.tick_history[-1]['mid_price']
            
            # Try to get strategy display information
            display_info = self._get_strategy_display_info(current_price)
            
            if display_info:
                # Only log if information has changed meaningfully
                if self._has_strategy_info_changed(display_info, current_price):
                    log.info(display_info)
                    self.last_strategy_info = display_info
                    self.last_strategy_info_time = current_time
            else:
                # Fallback to basic strategy state
                strategy_state = self.strategy.get_strategy_state()
                strategy_name = strategy_state.get('strategy_name', 'Unknown')
                basic_info = f"📊 {strategy_name} | Price: {current_price:.2f}"
                
                if self._has_strategy_info_changed(basic_info, current_price):
                    log.info(basic_info)
                    self.last_strategy_info = basic_info
                    self.last_strategy_info_time = current_time
                
        except Exception as e:
            log.error(f"Error logging strategy information: {e}")
    
    def _has_strategy_info_changed(self, current_info, current_price):
        """Check if strategy information has changed meaningfully."""
        # Always show first time
        if self.last_strategy_info is None:
            return True
            
        # Check if the information string has changed (excluding price)
        if self.last_strategy_info != current_info:
            # Extract non-price parts to see if there's a meaningful change
            current_non_price = self._extract_non_price_info(current_info)
            last_non_price = self._extract_non_price_info(self.last_strategy_info)
            
            # Show if non-price information changed
            if current_non_price != last_non_price:
                return True
        
        return False
    
    def _extract_non_price_info(self, info_string):
        """Extract non-price information from strategy info string."""
        try:
            # Remove price information to focus on strategy state changes
            import re
            # Remove patterns like "Price: 2650.45"
            non_price_info = re.sub(r'Price: \d+\.\d+', 'Price: X.XX', info_string)
            return non_price_info
        except:
            return info_string

    def _get_strategy_display_info(self, current_price):
        """Get display information from strategy if available."""
        try:
            # Check if strategy has a custom display method
            if hasattr(self.strategy, 'get_display_info'):
                return self.strategy.get_display_info(current_price)
            
            # Fallback to building display info from strategy state
            strategy_state = self.strategy.get_strategy_state()
            strategy_name = strategy_state.get('strategy_name', 'Unknown')
            
            return f"📊 {strategy_name} | Price: {current_price:.2f}"
            
        except Exception as e:
            log.debug(f"Error getting strategy display info: {e}")
            return None

    def _log_signal_events(self, signals, current_price):
        """Log important signal events using strategy-specific formatting."""
        current_time = self._get_current_time()
        
        for side, signal_data in signals:
            # Try to get signal description from strategy
            signal_description = self._get_signal_description(side, signal_data, current_price)
            
            if signal_description:
                # Check if this is a duplicate signal within short time frame
                if self._should_log_signal(signal_description, current_time):
                    log.info(signal_description)
                    self.last_signal_info = signal_description
                    self.last_signal_time = current_time
            else:
                # Fallback to generic signal logging
                signal_type = signal_data.get('signal_type', 'unknown')
                generic_signal = f"📊 SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: {signal_type}"
                
                if self._should_log_signal(generic_signal, current_time):
                    log.info(generic_signal)
                    self.last_signal_info = generic_signal
                    self.last_signal_time = current_time
    
    def _should_log_signal(self, signal_description, current_time):
        """Check if we should log this signal to prevent spam."""
        # Always log first signal
        if self.last_signal_time is None:
            return True
            
        # Prevent duplicate signals within 30 seconds
        min_signal_interval = 30  # seconds
        time_since_last = (current_time - self.last_signal_time).total_seconds()
        
        if time_since_last < min_signal_interval:
            # Check if it's the same signal type (ignore price changes)
            if self._extract_signal_type(signal_description) == self._extract_signal_type(self.last_signal_info):
                return False
        
        return True
    
    def _extract_signal_type(self, signal_description):
        """Extract signal type from description for duplicate detection."""
        try:
            # Extract the signal type part, ignoring price
            import re
            # Remove price information
            signal_type = re.sub(r'Price: \d+\.\d+', '', signal_description)
            # Remove zone price information
            signal_type = re.sub(r'Zone: \d+\.\d+', '', signal_type)
            return signal_type.strip()
        except:
            return signal_description
    
    def _get_signal_description(self, side, signal_data, current_price):
        """Get signal description from strategy if available."""
        try:
            # Check if strategy has a custom signal description method
            if hasattr(self.strategy, 'get_signal_description'):
                return self.strategy.get_signal_description(side, signal_data, current_price)
            
            # Fallback to building description from signal data
            signal_type = signal_data.get('signal_type', 'unknown')
            zone_info = signal_data.get('zone', {})
            zone_price = zone_info.get('price', 0)
            zone_type = signal_data.get('type', 'unknown')
            
            # Create appropriate log message based on signal type
            if signal_type == 'breakout':
                return f"🚀 BREAKOUT SIGNAL | {side.upper()} | Price: {current_price:.2f} | Zone: {zone_price:.2f} ({zone_type})"
            else:
                return f"📊 SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: {signal_type}"
                
        except Exception as e:
            log.debug(f"Error getting signal description: {e}")
            return None

    def get_tick_statistics(self):
        """Get current tick monitoring statistics."""
        if not self.tick_history:
            return None

        latest_tick = self.tick_history[-1]

        # Calculate statistics
        avg_spread = sum(tick['spread'] for tick in self.tick_history[-100:]) / min(len(self.tick_history), 100)
        price_changes = [self.tick_history[i]['mid_price'] - self.tick_history[i-1]['mid_price']
                        for i in range(1, min(len(self.tick_history), 101))]
        avg_price_change = sum(abs(pc) for pc in price_changes) / len(price_changes) if price_changes else 0

        return {
            'total_ticks': self.tick_stats['total_ticks'],
            'current_price': latest_tick['mid_price'],
            'current_bid': latest_tick['bid'],
            'current_ask': latest_tick['ask'],
            'current_spread': latest_tick['spread'],
            'average_spread': avg_spread,
            'tick_frequency': self.tick_stats['tick_frequency'],
            'price_volatility': avg_price_change,
            'last_tick_time': latest_tick['time'],
            'ticks_in_history': len(self.tick_history)
        }

    # ═══════════════════════════════════════════════════════════════════════════════
    # BROKER ABSTRACTION INTERFACE
    # ═══════════════════════════════════════════════════════════════════════════════
    # All methods below provide broker-agnostic interfaces that can be used by
    # other modules. The actual broker implementation (MT5) is handled internally.

    def broker_initialize(self):
        """Initialize broker connection. Returns True if successful."""
        return self._mt5_init()

    def broker_is_connected(self):
        """Check if broker connection is active. Returns True if connected."""
        return mt5.terminal_info() is not None

    def broker_ensure_connection(self):
        """Ensure broker connection is active. Returns True if connected."""
        return self.ensure_mt5_connection()

    def broker_get_current_price(self, symbol=None):
        """Get current bid/ask prices for symbol. Returns dict with 'bid', 'ask', 'time'."""
        if symbol is None:
            symbol = self.cfg.SYMBOL

        tick = mt5.symbol_info_tick(symbol)
        if not tick:
            return None

        return {
            'bid': tick.bid,
            'ask': tick.ask,
            'time': datetime.fromtimestamp(tick.time, self._get_consistent_timezone())
        }



    def broker_get_historical_data(self, symbol=None, timeframe=None, start_date=None, end_date=None, count=None):
        """Get historical OHLC data. Returns pandas DataFrame or None."""
        if symbol is None:
            symbol = self.cfg.SYMBOL
        if timeframe is None:
            timeframe = self.configured_timeframe

        return self._fetch_ohlc_data(symbol, timeframe, start_date, end_date, count)

    def broker_get_historical_ticks(self, symbol=None, start_date=None, end_date=None, count=None):
        """Get historical tick data from MT5. Returns list of tick dictionaries or None.

        Args:
            symbol: Trading symbol (defaults to config symbol)
            start_date: Start datetime for tick data
            end_date: End datetime for tick data
            count: Number of ticks to fetch (alternative to end_date)

        Returns:
            List of tick dictionaries with 'time', 'bid', 'ask' keys, or None if failed
        """
        if symbol is None:
            symbol = self.cfg.SYMBOL

        return self._fetch_tick_data(symbol, start_date, end_date, count)

    def broker_send_order(self, order_type, volume, symbol=None, price=None, sl=None, tp=None, comment="", magic=None):
        """Send order to broker. Returns order result dict or None."""
        if symbol is None:
            symbol = self.cfg.SYMBOL
        if magic is None:
            magic = self.cfg.MAGIC_NUMBER

        return self._execute_order(order_type, volume, symbol, price, sl, tp, comment, magic)

    def broker_get_positions(self, symbol=None):
        """Get open positions. Returns list of position dicts."""
        if symbol is None:
            symbol = self.cfg.SYMBOL
        return self._get_open_positions(symbol)

    def broker_close_position(self, position_ticket, volume=None):
        """Close position by ticket. Returns True if successful."""
        return self._close_position_by_ticket(position_ticket, volume)

    def broker_get_account_info(self):
        """Get account information. Returns dict with account details."""
        return self._get_account_details()

    def broker_validate_symbol(self, symbol=None):
        """Validate if symbol is available for trading. Returns True if valid."""
        if symbol is None:
            symbol = self.cfg.SYMBOL
        return self._validate_symbol(symbol)

    # ═══════════════════════════════════════════════════════════════════════════════
    # PRIVATE MT5 IMPLEMENTATION METHODS
    # ═══════════════════════════════════════════════════════════════════════════════
    # These methods contain the actual MT5-specific implementation and should not
    # be called directly by other modules. Use the broker_* methods above instead.

    def _mt5_init(self):
        """Initialize MT5 connection."""
        log.info("Connecting to MT5...")
        if not mt5.initialize(login=self.cfg.LOGIN, password=self.cfg.PASSWORD, server=self.cfg.SERVER):
            log.error(mt5.last_error())
            return False
        if not mt5.symbol_select(self.cfg.SYMBOL, True):
            log.error(f"Symbol select failed: {self.cfg.SYMBOL}")
            return False
        log.info("MT5 connection established.")
        return True

    def _validate_mt5_settings(self):
        """Validate MT5 connection, symbol, and account information."""
        info = mt5.symbol_info(self.cfg.SYMBOL)
        if not info or not info.visible:
            log.error(f"Invalid or unavailable symbol: {self.cfg.SYMBOL}")
            return False
        account_info = mt5.account_info()
        if not account_info:
            log.error("Failed to retrieve account info.")
            return False

        return True

    # ------------------ MT5 Connection and Data Fetching ------------------

    def _log_mt5_error(self, operation_name):
        """Log MT5 error details."""
        mt5_error = mt5.last_error()
        if mt5_error != (0, 'Success'):
            log.error(f"MT5 Error during {operation_name}: {mt5_error}")

    def _validate_ohlc_data(self, df):
        """Validate OHLC data integrity."""
        if df is None or len(df) == 0:
            return False

        required_cols = ['open', 'high', 'low', 'close']
        if not all(col in df.columns for col in required_cols):
            log.error(f"Missing required columns. Expected: {required_cols}, Got: {list(df.columns)}")
            return False

        if (df[required_cols] <= 0).any().any():
            log.error("Found negative or zero prices in OHLC data")
            return False

        invalid_rows = (df['high'] < df['low']) | (df['high'] < df['open']) | (df['high'] < df['close']) | \
                       (df['low'] > df['open']) | (df['low'] > df['close'])

        if invalid_rows.any():
            log.error(f"Found {invalid_rows.sum()} rows with invalid OHLC relationships")
            return False

        return True

    def _get_timeframe_seconds(self, timeframe):
        """Get seconds for a given MT5 timeframe."""
        timeframe_map = {
            mt5.TIMEFRAME_M1: 60,
            mt5.TIMEFRAME_M5: 300,
            mt5.TIMEFRAME_M15: 900,
            mt5.TIMEFRAME_M30: 1800,
            mt5.TIMEFRAME_H1: 3600,
            mt5.TIMEFRAME_H4: 14400,
            mt5.TIMEFRAME_D1: 86400,
        }
        return timeframe_map.get(timeframe, 60)

    def ensure_mt5_connection(self):
        """Ensure connection to broker terminal."""
        if not self.broker_is_connected():
            log.warning("Broker connection lost. Attempting to reconnect...")
            if not self.broker_initialize():
                log.error("Reconnection failed.")
                return False
            log.info("Broker reconnected successfully.")
        return True

    def _fetch_ohlc_data(self, symbol, timeframe, start_date=None, end_date=None, count=None):
        """Private method to fetch OHLC data from MT5."""
        try:
            if start_date and end_date:
                rates = mt5.copy_rates_range(symbol, timeframe, start_date, end_date)
            elif start_date and count:
                rates = mt5.copy_rates_from(symbol, timeframe, start_date, count)
            else:
                log.error("Either (start_date, end_date) or (start_date, count) must be provided")
                return None

            if rates is None or len(rates) == 0:
                log.error(f"No historical data received for {symbol}")
                return None

            df = pd.DataFrame(rates)[["time", "open", "high", "low", "close"]]
            df["time"] = pd.to_datetime(df["time"], unit="s", utc=True)
            df.set_index("time", inplace=True)

            if not self._validate_ohlc_data(df):
                log.error("OHLC data validation failed")
                return None

            return df

        except Exception as e:
            log.error(f"Error fetching OHLC data: {e}")
            self._log_mt5_error("fetching OHLC data")
            return None

    def _fetch_tick_data(self, symbol, start_date=None, end_date=None, count=None):
        """Private method to fetch tick data from MT5.

        Args:
            symbol: Trading symbol
            start_date: Start datetime for tick data
            end_date: End datetime for tick data
            count: Number of ticks to fetch (alternative to end_date)

        Returns:
            List of tick dictionaries with 'time', 'bid', 'ask' keys, or None if failed
        """
        try:
            import MetaTrader5 as mt5

            if start_date and end_date:
                # Use copy_ticks_range for date range
                ticks = mt5.copy_ticks_range(symbol, start_date, end_date, mt5.COPY_TICKS_ALL)
            elif start_date and count:
                # Use copy_ticks_from for count from start date
                ticks = mt5.copy_ticks_from(symbol, start_date, count, mt5.COPY_TICKS_ALL)
            else:
                log.error("Either (start_date, end_date) or (start_date, count) must be provided for tick data")
                return None

            if ticks is None or len(ticks) == 0:
                log.warning(f"No tick data received for {symbol} from {start_date} to {end_date}")
                return None

            # Convert MT5 tick data to our standard format
            tick_list = []
            tz = self._get_consistent_timezone()

            for tick in ticks:
                # MT5 returns numpy structured arrays, access fields by index or name
                tick_dict = {
                    'time': datetime.fromtimestamp(tick['time'], tz),
                    'bid': float(tick['bid']),
                    'ask': float(tick['ask'])
                }
                tick_list.append(tick_dict)

            log.debug(f"Retrieved {len(tick_list)} ticks for {symbol}")
            return tick_list

        except Exception as e:
            log.error(f"Error fetching tick data: {e}")
            self._log_mt5_error("fetching tick data")
            return None

    def _execute_order(self, order_type, volume, symbol, price, sl, tp, comment, magic):
        """Private method to execute order via MT5."""
        # Map order types
        if order_type.lower() == "buy":
            mt5_order_type = mt5.ORDER_TYPE_BUY
        elif order_type.lower() == "sell":
            mt5_order_type = mt5.ORDER_TYPE_SELL
        else:
            log.error(f"Invalid order type: {order_type}")
            return None

        req = {
            "action": mt5.TRADE_ACTION_DEAL,
            "symbol": symbol,
            "volume": volume,
            "type": mt5_order_type,
            "price": price,
            "sl": sl,
            "tp": tp,
            "magic": magic,
            "comment": comment,
            "type_time": mt5.ORDER_TIME_GTC,
            "type_filling": mt5.ORDER_FILLING_IOC,
            "deviation": self.cfg.ORDER_DEVIATION,
        }

        try:
            result = mt5.order_send(req)
            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                return {
                    "success": True,
                    "order_ticket": result.order,
                    "retcode": result.retcode,
                    "comment": result.comment
                }
            else:
                log.error(f"Order failed: {result.comment if result else 'Unknown error'}")
                return {
                    "success": False,
                    "retcode": result.retcode if result else -1,
                    "comment": result.comment if result else "Unknown error"
                }
        except Exception as e:
            log.error(f"Error executing order: {e}")
            return None

    def _get_open_positions(self, symbol):
        """Private method to get open positions from MT5."""
        try:
            positions = mt5.positions_get(symbol=symbol)
            if not positions:
                return []

            position_list = []
            for pos in positions:
                position_list.append({
                    "ticket": pos.ticket,
                    "symbol": pos.symbol,
                    "type": "buy" if pos.type == mt5.ORDER_TYPE_BUY else "sell",
                    "volume": pos.volume,
                    "price_open": pos.price_open,
                    "sl": pos.sl,
                    "tp": pos.tp,
                    "profit": pos.profit,
                    "comment": pos.comment,
                    "magic": pos.magic
                })
            return position_list

        except Exception as e:
            log.error(f"Error getting positions: {e}")
            return []

    def _close_position_by_ticket(self, ticket, volume=None):
        """Private method to close position by ticket via MT5."""
        try:
            positions = mt5.positions_get(ticket=ticket)
            if not positions:
                log.error(f"Position {ticket} not found")
                return False

            pos = positions[0]
            close_type = mt5.ORDER_TYPE_SELL if pos.type == mt5.ORDER_TYPE_BUY else mt5.ORDER_TYPE_BUY
            close_volume = volume if volume else pos.volume

            tick = mt5.symbol_info_tick(pos.symbol)
            if not tick:
                log.error(f"Could not get tick for {pos.symbol}")
                return False

            price = tick.bid if pos.type == mt5.ORDER_TYPE_BUY else tick.ask

            close_request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": pos.symbol,
                "volume": close_volume,
                "type": close_type,
                "position": ticket,
                "price": price,
                "magic": pos.magic,
                "comment": "Close by BaseTrader",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(close_request)
            if result and result.retcode == mt5.TRADE_RETCODE_DONE:
                log.info(f"Position {ticket} closed successfully")
                return True
            else:
                log.error(f"Failed to close position {ticket}: {result.comment if result else 'Unknown error'}")
                return False

        except Exception as e:
            log.error(f"Error closing position {ticket}: {e}")
            return False

    def _get_account_details(self):
        """Private method to get account information from MT5."""
        try:
            account_info = mt5.account_info()
            if not account_info:
                return None

            return {
                "balance": account_info.balance,
                "equity": account_info.equity,
                "margin": account_info.margin,
                "free_margin": account_info.margin_free,
                "profit": account_info.profit,
                "currency": account_info.currency,
                "leverage": account_info.leverage
            }
        except Exception as e:
            log.error(f"Error getting account info: {e}")
            return None

    def _validate_symbol(self, symbol):
        """Private method to validate symbol via MT5."""
        try:
            info = mt5.symbol_info(symbol)
            return info is not None and info.visible
        except Exception as e:
            log.error(f"Error validating symbol {symbol}: {e}")
            return False

    def pull_1m(self, lookback_days, start=None, end=None):
        """Pull 1-minute OHLC data using broker abstraction."""
        tz = self._get_consistent_timezone()

        if start is None or end is None:
            end = datetime.now(tz)
            start = end - timedelta(days=lookback_days)

        return self.broker_get_historical_data(
            symbol=self.cfg.SYMBOL,
            timeframe=self.TIMEFRAME_M1,
            start_date=start,
            end_date=end
        )

    def pull_ohlc(self, lookback_days, backtest_days, start=None, end=None, timeframe=None):
        """Pull OHLC data for any timeframe using broker abstraction."""
        tz = self._get_consistent_timezone()

        if timeframe is None:
            timeframe = self.configured_timeframe

        if start is None or end is None:
            end = datetime.now(tz)
            days_to_look_back = backtest_days if timeframe != self.TIMEFRAME_M1 else lookback_days
            start = end - timedelta(days=days_to_look_back)

        log.debug(f"Fetching {self.cfg.SYMBOL} data from {start} to {end}")

        # Try primary method first
        df = self.broker_get_historical_data(
            symbol=self.cfg.SYMBOL,
            timeframe=timeframe,
            start_date=start,
            end_date=end
        )

        if df is None or df.empty:
            # Try alternative method with count
            log.debug(f"Trying alternative method: copy_rates_from")
            timeframe_seconds = self._get_timeframe_seconds(timeframe)
            if timeframe_seconds <= 0:
                log.error(f"Invalid timeframe seconds: {timeframe_seconds}")
                return None

            total_seconds = (end - start).total_seconds()
            if total_seconds <= 0:
                log.error(f"Invalid time range: start={start}, end={end}")
                return None

            count = int(total_seconds / timeframe_seconds)
            df = self.broker_get_historical_data(
                symbol=self.cfg.SYMBOL,
                timeframe=timeframe,
                start_date=start,
                count=count
            )

            if df is None or df.empty:
                log.error(f"Alternative method also failed for {self.cfg.SYMBOL}")
                return None

        log.debug(f"Retrieved {len(df)} data points for {self.cfg.SYMBOL}")
        return df

    # ---------------------- Trade and Position Management ----------------------

    def _manage_trade_history(self):
        """Manage trade history size to prevent memory leaks."""
        if len(self.trade_history) > self.max_trade_history_size:
            # Archive old closed trades
            closed_trades = [t for t in self.trade_history if t.get("status") in ["Win", "Loss"]]
            if len(closed_trades) > self.max_trade_history_size // 2:
                # Keep recent closed trades, archive the rest
                closed_trades.sort(key=lambda t: t.get('close_time', datetime.min))
                trades_to_archive = closed_trades[:-self.max_trade_history_size // 4]

                # Move to archive
                self.archived_trades.extend(trades_to_archive)

                # Remove from active history
                archived_tickets = {t.get('order_ticket') for t in trades_to_archive}
                self.trade_history = [t for t in self.trade_history
                                    if t.get('order_ticket') not in archived_tickets]

                log.info(f"Archived {len(trades_to_archive)} old trades. "
                        f"Active history: {len(self.trade_history)}, "
                        f"Archived: {len(self.archived_trades)}")

    def get_open_positions_count(self, trade_history=None):
        """Get the number of currently open positions."""
        if trade_history is not None:
            # For backtesting - count directly from provided history
            return sum(1 for trade in trade_history if trade.get("status") is None)

        # For live trading - use thread-safe cached count
        with self._position_count_lock:
            return self._cached_open_positions_count

    def _sync_open_positions_count(self):
        """Fetch all open positions from broker and update the cached count."""
        # This is a full sync, so it should be used sparingly.
        with self._position_count_lock:
            positions = self.broker_get_positions(self.cfg.SYMBOL)
            count = len(positions) if positions else 0
            if count != self._cached_open_positions_count:
                log.info(f"Position count synced. Cached: {self._cached_open_positions_count}, Broker: {count}. Cache updated.")
                self._cached_open_positions_count = count
            return count

    def _update_cached_position_count(self, delta):
        """Update cached position count incrementally."""
        with self._position_count_lock:
            self._cached_open_positions_count = max(0, self._cached_open_positions_count + delta)
            log.debug(f"Position count incrementally updated by {delta} to {self._cached_open_positions_count}")

    def position_exists(self, trade_history=None):
        """Check if a position exists for the given symbol."""
        if trade_history is not None:
            # For backtesting
            return any(trade["status"] is None for trade in trade_history)

        positions = self.broker_get_positions(self.cfg.SYMBOL)
        return positions and len(positions) > 0

    def send_order(self, side, lot, sl, tp, price, sequence_id, position_id=None):
        """Send a market order using broker abstraction with retry logic."""
        comment = f"ZoneBoS_{sequence_id}"
        if position_id is not None:
            comment = f"ZoneBoS_P{position_id}_{sequence_id}"

        max_retries = getattr(self.cfg, 'ORDER_MAX_RETRIES', 3)
        retry_delay = getattr(self.cfg, 'ORDER_RETRY_DELAY', 1)

        for attempt in range(max_retries):
            result = self.broker_send_order(
                order_type=side.lower(),
                volume=lot,
                symbol=self.cfg.SYMBOL,
                price=price,
                sl=sl,
                tp=tp,
                comment=comment,
                magic=self.cfg.MAGIC_NUMBER
            )

            if result and result.get("success"):
                position_info = f" | Position: {position_id}" if position_id else ""
                log.info(f"LIVE TRADE OPENED: {side} @ {price:.2f} | Lot: {lot:.2f} | SL: {sl:.2f} | TP: {tp:.2f}{position_info}")
                trade = {
                    "type": side,
                    "lot": lot,
                    "order_ticket": result.get("order_ticket", 0),
                    "entry_price": price,
                    "sl": sl,
                    "tp": tp,
                    "profit": 0.0,
                    "status": None,
                    "entry_time": self._get_current_time(),
                    "close_time": None,
                    "duration": None,
                    "martingale_sequence_id": sequence_id,
                    "position_id": position_id if position_id is not None else sequence_id
                }
                # Update cached position count
                self._update_cached_position_count(1)
                return trade
            else:
                # Failure analysis
                retcode = result.get("retcode") if result else None
                recoverable_codes = [
                    10004,  # TRADE_RETCODE_REQUOTE
                    10016,  # TRADE_RETCODE_PRICE_OFF
                    10022,  # TRADE_RETCODE_TIMEOUT
                    10018,  # TRADE_RETCODE_SERVER_BUSY
                    10021   # TRADE_RETCODE_INVALID_REQUEST
                ]

                if retcode in recoverable_codes:
                    log.warning(f"Order failed with recoverable error {retcode}. Retrying ({attempt + 1}/{max_retries})...")
                    time.sleep(retry_delay)
                    # On requote or price off, refresh price
                    if retcode in [10004, 10016]:
                        tick = self.broker_get_current_price()
                        if tick:
                            price = tick['ask'] if side == "Buy" else tick['bid']
                            log.info(f"Refreshed price to {price:.2f} for retry.")
                    continue
                else:
                    # Unrecoverable error
                    error_msg = result.get("comment", "Unknown error") if result else "No result returned"
                    log.error(f"Order failed with unrecoverable error: {error_msg} (code: {retcode})")
                    return None
        
        log.error(f"Order failed after {max_retries} retries.")
        return None

    def close_all_positions(self):
        """Close all open positions for the given symbol."""
        positions = self.broker_get_positions(self.cfg.SYMBOL)
        if not positions:
            log.info("No positions to close.")
            return

        for pos in positions:
            success = self.broker_close_position(pos["ticket"])
            if success:
                log.info(f"Closed position {pos['ticket']} successfully.")
            else:
                log.error(f"Failed to close position {pos['ticket']}")

    def _initialize_strategy(self):
        """Initialize strategy using strategy-specific logic."""
        log.info("Initializing strategy...")

        # Get historical data for strategy initialization using configured timeframe
        df: pd.DataFrame = self.pull_ohlc(
            lookback_days=self.cfg.LOOKBACK_DAYS,
            backtest_days=self.cfg.LOOKBACK_DAYS,
            timeframe=self.configured_timeframe
        )
        if df is None or df.empty:
            log.error(f"Failed to fetch initial data for strategy initialization.")
            raise RuntimeError("Insufficient data for strategy initialization")

        # Safe access to current price with validation
        try:
            current_price = df['close'].iloc[-1]
            if current_price <= 0:
                log.error(f"Invalid current price for strategy initialization: {current_price}")
                raise RuntimeError("Invalid price data for strategy initialization")
        except (IndexError, KeyError) as e:
            log.error(f"Error accessing price data for strategy initialization: {e}")
            raise RuntimeError("Cannot access price data for strategy initialization")

        # Initialize strategy with current market data
        self.strategy.initialize(current_price, df)

        log.info(f"Strategy initialized successfully with current price {current_price:.2f}")

        # Get strategy state for logging
        state = self.strategy.get_strategy_state()
        log.debug(f"Strategy state: {state}")

    def _update_strategy(self, current_price, current_time=None):
        """Update strategy state with current market conditions."""
        if current_time is None:
            current_time = self._get_current_time()

        # Update strategy with current market data
        self.strategy.update(current_price, current_time)

    def evaluate_entry_signals(self, tick_price, current_time=None):
        """Evaluate entry signals using strategy-specific logic."""
        if current_time is None:
            current_time = self._get_current_time()

        signals = self.strategy.get_entry_signals(tick_price, self._prev_tick_price, current_time)
        self._prev_tick_price = tick_price
        return signals

    def _is_within_trading_hours(self, current_time=None):
        """Check if current time is within configured trading hours."""
        if current_time is None:
            current_time = self._get_current_time()

        current_hour = current_time.hour
        return self.cfg.TRADING_START_HOUR <= current_hour < self.cfg.TRADING_END_HOUR

    def _check_daily_profit_target(self, current_day_key=None):
        """Check if daily profit target has been reached."""
        if not self.cfg.ENABLE_PROFIT_TARGET:
            return False

        if current_day_key is None:
            current_time = self._get_current_time()
            current_day_key = current_time.strftime("%Y-%m-%d")

        daily_profit = self.daily_profit.get(current_day_key, 0.0)
        target_reached = daily_profit >= self.cfg.DAILY_PROFIT_TARGET

        if target_reached and self.cfg.STOP_ON_PROFIT_TARGET:
            log.info(f"Daily profit target of ${self.cfg.DAILY_PROFIT_TARGET:.2f} reached! Current profit: ${daily_profit:.2f}")
            return True

        return False

    def _should_continue_trading(self, current_time=None):
        """Check if trading should continue based on hours and profit target."""
        if current_time is None:
            current_time = self._get_current_time()

        # Check trading hours
        if not self._is_within_trading_hours(current_time):
            return False, "Outside trading hours"

        # Check daily profit target
        current_day_key = current_time.strftime("%Y-%m-%d")
        if self._check_daily_profit_target(current_day_key):
            return False, "Daily profit target reached"

        return True, "Trading allowed"

    def _get_martingale_step_from_lot(self, base_lot_size=None, trade_history=None, position_id=None):
        """Calculate martingale lot size based on base lot size for a specific position."""
        if base_lot_size is None:
            base_lot_size = self.cfg.BASE_LOT_SIZE

        return self._calculate_martingale_lot(
            trade_history if trade_history is not None else self.trade_history,
            position_id,
            base_lot_size,  # Use provided base lot size instead of config
            self.cfg.MARTINGALE_MULT,
            self.cfg.MAX_SAFE_LOT_SIZE,
            self.current_martingale_sequence_id,
            self.cfg.ENABLE_MARTINGALE,
            self.cfg.MAX_MART_STEPS
        )

    def _position_exists(self, backtest_trades=None):
        """Check if position exists."""
        return self.position_exists(
            trade_history=backtest_trades if backtest_trades is not None else None
        )

    def _simulate_order(self, side, lot, sl_d, tp_d, tick, trade_time, sequence_id, position_id=None):
        """Simulate order for backtesting."""
        return self._create_simulated_trade(side, lot, sl_d, tp_d, tick, trade_time, sequence_id, position_id)

    def _close_all_positions(self):
        """Close all open positions."""
        self.close_all_positions()

    # Multi-position methods

    def _get_next_available_position_slot(self):
        """Get the next available position slot (1-MAX_OPEN_POSITIONS) or None if all slots are occupied."""
        if not self.available_position_slots:
            log.warning(f"No available position slots. Active slots: {list(self.active_position_slots.keys())}, "
                       f"Available slots: {self.available_position_slots}, "
                       f"Open positions count: {self.get_open_positions_count()}")
            return None
        
        slot_id = self.available_position_slots[0]
        log.debug(f"Next available position slot: {slot_id} | Available: {self.available_position_slots} | Active: {list(self.active_position_slots.keys())}")
        return slot_id

    def _allocate_position_slot(self, position_id):
        """Allocate a position slot and mark it as active."""
        if position_id in self.available_position_slots:
            self.available_position_slots.remove(position_id)
            self.active_position_slots[position_id] = {"status": "open", "last_trade": None}
            log.debug(f"Allocated position slot {position_id} | Available: {self.available_position_slots} | Active: {list(self.active_position_slots.keys())}")
            return True
        log.warning(f"Failed to allocate position slot {position_id} - not available. Available: {self.available_position_slots}, Active: {list(self.active_position_slots.keys())}")
        return False

    def _release_position_slot(self, position_id):
        """Release a position slot and make it available for reuse."""
        if position_id in self.active_position_slots:
            del self.active_position_slots[position_id]
            if position_id not in self.available_position_slots:
                self.available_position_slots.append(position_id)
                self.available_position_slots.sort()  # Keep slots in order
            log.debug(f"Released position slot {position_id} | Available: {self.available_position_slots} | Active: {list(self.active_position_slots.keys())}")
            return True
        log.warning(f"Failed to release position slot {position_id} - not active. Active slots: {list(self.active_position_slots.keys())}")
        return False

    def _update_position_slot_status(self, trades=None):
        """Update position slot status based on current trade history."""
        trade_history = trades if trades is not None else self.trade_history

        # Get all currently open trades
        open_trades = [t for t in trade_history if t.get("status") is None]
        open_position_ids = {t.get("position_id") for t in open_trades if t.get("position_id")}

        # Release slots that no longer have open trades
        for position_id in list(self.active_position_slots.keys()):
            if position_id not in open_position_ids:
                self._release_position_slot(position_id)

        # Allocate slots for open trades that aren't tracked yet
        for position_id in open_position_ids:
            if position_id and position_id not in self.active_position_slots:
                self._allocate_position_slot(position_id)

    def _update_open_positions_profit(self):
        """Update the last known profit for open positions."""
        try:
            positions = mt5.positions_get(symbol=self.cfg.SYMBOL)
            if not positions:
                return
            
            # Update last known profit for open trades
            for trade in self.trade_history:
                if trade.get("status") is None:  # Open trade
                    ticket = trade["order_ticket"]
                    current_position = next((p for p in positions if p.ticket == ticket), None)
                    if current_position:
                        trade["last_known_profit"] = current_position.profit
                        
        except Exception as e:
            log.error(f"Error updating open positions profit: {e}")

    def _update_trade_history(self):
        """Update trade history by checking MT5 for closed positions."""
        try:
            tz = self._get_consistent_timezone()
            trades_closed = False
            position_status_needs_update = False
            
            for trade in self.trade_history:
                if trade.get("status") is None:
                    ticket = trade["order_ticket"]
                    positions = mt5.positions_get(symbol=self.cfg.SYMBOL)
                    
                    # Check if position still exists and get its current profit
                    current_position = None
                    if positions:
                        current_position = next((p for p in positions if p.ticket == ticket), None)
                        if current_position:
                            continue
                    
                    # Position is no longer in MT5 positions list - it's closed
                    deals = mt5.history_deals_get(ticket=ticket)
                    if deals:
                        profit = sum(d.profit for d in deals if d.entry == mt5.DEAL_ENTRY_OUT)
                        close_time = datetime.fromtimestamp(deals[-1].time, tz)
                        entry_time = trade["entry_time"]

                        # Robust datetime parsing
                        entry_time = self._parse_datetime_robust(entry_time, tz)
                        if entry_time is None:
                            log.error(f"Could not parse entry_time for trade {ticket}: {trade['entry_time']}")
                            continue

                        trade["profit"] = profit
                        trade["status"] = "Win" if profit > 0 else "Loss"
                        trade["close_time"] = close_time
                        trade["exit_price"] = deals[-1].price
                        trade["duration"] = close_time - entry_time
                        trade["entry_time"] = entry_time

                        day_key = entry_time.strftime("%Y-%m-%d")
                        if day_key not in self.daily_profit:
                            self.daily_profit[day_key] = 0.0
                        self.daily_profit[day_key] += profit

                        # Update cached position count
                        self._update_cached_position_count(-1)
                        trades_closed = True

                        # Notify strategy of trade closure
                        trade['open_positions_count'] = self.get_open_positions_count()
                        self.strategy.on_trade_closed(trade)

                        log.info(f"TRADE CLOSED: {trade['type']} | Lot: {trade['lot']:.2f} | Profit: {profit:.2f} ({trade['status']}) | Duration: {trade['duration']}")
                    else:
                        # Position is closed but deals not available yet 
                        # Try to get profit from stored position data if available
                        stored_profit = trade.get("last_known_profit", 0.0)
                        
                        log.warning(f"Position {ticket} closed but deals not available yet. Using last known profit: {stored_profit:.2f}")
                        trade["status"] = "Win" if stored_profit > 0 else "Loss"
                        trade["close_time"] = self._get_current_time()
                        trade["profit"] = stored_profit
                        
                        # Add to daily profit
                        current_day = self._get_current_time().strftime("%Y-%m-%d")
                        if current_day not in self.daily_profit:
                            self.daily_profit[current_day] = 0.0
                        self.daily_profit[current_day] += stored_profit
                        
                        # Update cached position count
                        self._update_cached_position_count(-1)
                        position_status_needs_update = True

                        # Notify strategy of trade closure
                        trade['open_positions_count'] = self.get_open_positions_count()
                        self.strategy.on_trade_closed(trade)

            # Update position slot status if any trades were closed OR if position status needs update
            if trades_closed or position_status_needs_update:
                self._update_position_slot_status()
                log.debug(f"Position slots updated after trade closure. Available slots: {len(self.available_position_slots)}")

            # Manage trade history size
            self._manage_trade_history()

        except Exception as e:
            log.error(f"Error in _update_trade_history: {e}")

    def _resolve_pending_closed_trades(self):
        """Resolve trades with 'Closed_No_Deals' status by trying to get deal information."""
        try:
            tz = self._get_consistent_timezone()
            resolved_count = 0
            
            for trade in self.trade_history:
                if trade.get("status") == "Closed_No_Deals":
                    ticket = trade["order_ticket"]
                    deals = mt5.history_deals_get(ticket=ticket)
                    
                    if deals:
                        profit = sum(d.profit for d in deals if d.entry == mt5.DEAL_ENTRY_OUT)
                        close_time = datetime.fromtimestamp(deals[-1].time, tz)
                        entry_time = trade["entry_time"]

                        # Robust datetime parsing
                        entry_time = self._parse_datetime_robust(entry_time, tz)
                        if entry_time is None:
                            log.error(f"Could not parse entry_time for trade {ticket}: {trade['entry_time']}")
                            continue

                        # Update trade with full deal information
                        trade["profit"] = profit
                        trade["status"] = "Win" if profit > 0 else "Loss"
                        trade["close_time"] = close_time
                        trade["exit_price"] = deals[-1].price
                        trade["duration"] = close_time - entry_time
                        trade["entry_time"] = entry_time

                        # Update daily profit
                        day_key = entry_time.strftime("%Y-%m-%d")
                        if day_key not in self.daily_profit:
                            self.daily_profit[day_key] = 0.0
                        self.daily_profit[day_key] += profit

                        resolved_count += 1

                        # Notify strategy of trade closure
                        trade['open_positions_count'] = self.get_open_positions_count()
                        self.strategy.on_trade_closed(trade)

                        log.info(f"RESOLVED TRADE: {trade['type']} | Lot: {trade['lot']:.2f} | Profit: {profit:.2f} ({trade['status']}) | Duration: {trade['duration']}")
            
            if resolved_count > 0:
                log.info(f"Resolved {resolved_count} trades with pending deal information")
                
        except Exception as e:
            log.error(f"Error in _resolve_pending_closed_trades: {e}")

    def _parse_datetime_robust(self, dt_input, default_tz):
        """Robustly parse datetime from various formats."""
        if dt_input is None:
            return None

        if isinstance(dt_input, datetime):
            # Already a datetime object
            if dt_input.tzinfo is None:
                return dt_input.replace(tzinfo=default_tz)
            return dt_input

        if isinstance(dt_input, str):
            # Try multiple datetime formats
            formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d %H:%M:%S.%f",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%dT%H:%M:%S.%f",
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%dT%H:%M:%S.%fZ"
            ]

            for fmt in formats:
                try:
                    parsed_dt = datetime.strptime(dt_input, fmt)
                    return parsed_dt.replace(tzinfo=default_tz)
                except ValueError:
                    continue

            log.warning(f"Could not parse datetime string: {dt_input}")
            return None

        log.warning(f"Unsupported datetime type: {type(dt_input)}")
        return None

    def get_martingale_state_for_position(self, position_id):
        """Get the current martingale state for a specific position.
        
        This method helps debug and monitor the martingale system by showing
        the current state of a specific position's martingale sequence.
        
        Args:
            position_id: The position ID to check
            
        Returns:
            Dictionary containing martingale state information
        """
        if not getattr(self.cfg, 'ENABLE_MARTINGALE', False):
            return {
                'position_id': position_id,
                'martingale_enabled': False,
                'message': 'Martingale is disabled'
            }
            
        if position_id is None:
            return {
                'position_id': None,
                'martingale_enabled': True,
                'message': 'Position ID is required for martingale calculation'
            }
            
        # Filter trades for this position
        position_trades = [t for t in self.trade_history if t.get("position_id") == position_id]
        closed_trades = [t for t in position_trades if t.get("status") in ["Win", "Loss"] and t.get('close_time') is not None]
        
        if not closed_trades:
            return {
                'position_id': position_id,
                'martingale_enabled': True,
                'consecutive_losses': 0,
                'total_trades': len(position_trades),
                'closed_trades': 0,
                'next_lot_size': self.cfg.BASE_LOT_SIZE,
                'message': 'First trade for this position - using base lot size'
            }
            
        # Find consecutive losses
        consecutive_losses = 0
        for trade in sorted(closed_trades, key=lambda t: t['close_time'], reverse=True):
            if trade['status'] == 'Loss':
                consecutive_losses += 1
            else:
                break
                
        # Calculate next lot size
        if consecutive_losses == 0:
            next_lot = self.cfg.BASE_LOT_SIZE
            message = 'Last trade was a win - martingale reset'
        else:
            try:
                next_lot = self.cfg.BASE_LOT_SIZE * (self.cfg.MARTINGALE_MULT ** consecutive_losses)
                next_lot = min(next_lot, self.cfg.MAX_SAFE_LOT_SIZE)
                message = f'Consecutive losses: {consecutive_losses} - lot size increased'
            except (ValueError, OverflowError):
                next_lot = self.cfg.BASE_LOT_SIZE
                message = 'Lot size calculation error - using base lot'
                
        return {
            'position_id': position_id,
            'martingale_enabled': True,
            'consecutive_losses': consecutive_losses,
            'total_trades': len(position_trades),
            'closed_trades': len(closed_trades),
            'next_lot_size': next_lot,
            'base_lot_size': self.cfg.BASE_LOT_SIZE,
            'max_safe_lot_size': self.cfg.MAX_SAFE_LOT_SIZE,
            'message': message
        }

    def _calculate_martingale_lot(self, trade_history, position_id, base_lot_size, martingale_mult, max_safe_lot_size, current_martingale_sequence_id, enable_martingale=True, max_mart_steps=6):
        """Calculate the next lot size based on martingale strategy for a specific position.
        
        This method ensures each position has its own independent martingale sequence.
        When a position loses, only that position's next trade will have increased lot size.
        Other positions continue with their default lot size.
        """
        # If martingale is disabled, always use base lot size
        if not enable_martingale:
            log.debug(f"Position {position_id}: Martingale disabled, using base lot size {base_lot_size:.2f}")
            return 0, base_lot_size, current_martingale_sequence_id + 1

        # Validate position_id is provided for multi-position safety
        if position_id is None:
            log.warning("Martingale calculation requires a position_id for multi-position safety. Resetting.")
            return 0, base_lot_size, current_martingale_sequence_id + 1

        # Filter for closed trades for the specific position_id only
        position_trades = [t for t in trade_history if t.get("position_id") == position_id]
        
        # Filter for trades that are actually closed and have a valid close_time
        closed_trades = [t for t in position_trades if t.get("status") in ["Win", "Loss"] and t.get('close_time') is not None]

        if not closed_trades:
            # First trade for this position, use base lot
            log.debug(f"Position {position_id}: First trade, using base lot size {base_lot_size:.2f}")
            return 0, base_lot_size, current_martingale_sequence_id + 1

        # Find the last closed trade for this position
        last_closed_trade = max(closed_trades, key=lambda t: t['close_time'])

        # If the last closed trade for this position was a win, reset martingale for this position
        if last_closed_trade['status'] == 'Win':
            log.info(f"Position {position_id}: Last closed trade was a WIN. Resetting martingale to base lot {base_lot_size:.2f}")
            return 0, base_lot_size, current_martingale_sequence_id + 1

        # If the last closed trade was a loss, count consecutive losses for this position only
        consecutive_losses = 0
        for trade in sorted(closed_trades, key=lambda t: t['close_time'], reverse=True):
            if trade['status'] == 'Loss':
                consecutive_losses += 1
            else:
                # Stop at the first win encountered when iterating backwards
                break

        if consecutive_losses == 0:
            # This case should ideally not be reached if the last trade was a loss, but as a safeguard:
            log.warning(f"Position {position_id}: Unexpected state - last trade was loss but consecutive_losses=0. Using base lot.")
            return 0, base_lot_size, current_martingale_sequence_id + 1

        # Limit martingale steps to maximum allowed
        if consecutive_losses > max_mart_steps:
            log.warning(f"Position {position_id}: Consecutive losses ({consecutive_losses}) "
                        f"exceed max martingale steps ({max_mart_steps}). Resetting to base lot {base_lot_size:.2f}.")
            return 0, base_lot_size, current_martingale_sequence_id + 1

        try:
            # Martingale Logic: multiply base lot by (multiplier ^ consecutive_losses)
            next_lot = base_lot_size * (martingale_mult ** consecutive_losses)

            # Apply safety limit
            next_lot = min(next_lot, max_safe_lot_size)

            log.info(f"Position {position_id}: Martingale calculation: Consecutive losses = {consecutive_losses}. "
                     f"Lot size = {base_lot_size:.2f} * ({martingale_mult}^{consecutive_losses}) = {next_lot:.2f}")

            return consecutive_losses, next_lot, current_martingale_sequence_id + 1

        except (ValueError, OverflowError) as e:
            log.warning(f"Position {position_id}: Invalid lot size calculation: {e}. Resetting to base lot {base_lot_size:.2f}.")
            return 0, base_lot_size, current_martingale_sequence_id + 1

    def _create_simulated_trade(self, side, lot, sl_points, tp_points, tick, tick_time, sequence_id, position_id=None):
        """Create a simulated trade for backtesting."""
        # Validate and convert parameters to ensure they are numbers
        try:
            # Handle empty list case specifically
            if isinstance(lot, list):
                if len(lot) == 0:
                    log.error("Received empty list for lot size, using default BASE_LOT_SIZE")
                    lot = getattr(self.cfg, 'BASE_LOT_SIZE', 0.01)
                else:
                    log.error(f"Received non-empty list for lot size: {lot}, using first element")
                    lot = float(lot[0])
            else:
                lot = float(lot) if not isinstance(lot, (int, float)) else lot

            sl_points = float(sl_points) if not isinstance(sl_points, (int, float)) else sl_points
            tp_points = float(tp_points) if not isinstance(tp_points, (int, float)) else tp_points
        except (ValueError, TypeError) as e:
            log.error(f"Invalid parameter types in _create_simulated_trade: lot={type(lot)}, sl_points={type(sl_points)}, tp_points={type(tp_points)}")
            log.error(f"Parameter values: lot={lot}, sl_points={sl_points}, tp_points={tp_points}")
            raise ValueError(f"Invalid parameter types: {e}")

        price = tick['ask'] if side == "Buy" else tick['bid']
        sl = price - sl_points if side == "Buy" else price + sl_points
        tp = price + tp_points if side == "Buy" else price - tp_points

        trade = {
            "type": side,
            "lot": lot,
            "order_ticket": 0,  # Placeholder for backtesting
            "entry_price": price,
            "sl": sl,
            "tp": tp,
            "profit": 0.0,
            "status": None,
            "entry_time": tick_time,
            "close_time": None,
            "duration": None,
            "exit_price": 0.0,
            "martingale_sequence_id": sequence_id,
            "position_id": position_id if position_id is not None else sequence_id,
        }

        position_info = f" | Pos: {trade['position_id']}" if trade.get("position_id") is not None else ""
        log.debug(f"SIMULATED TRADE: {side} @ {price:.2f} | Lot: {lot:.2f} | SL: {sl:.2f} | TP: {tp:.2f}{position_info}")
        return trade

    def run(self, backtest=False):
        """Main trading loop for live trading or backtesting."""
        if backtest:
            log.info("Starting backtest mode...")
            self._run_backtest()
        else:
            self._run_live()

    def _run_backtest(self):
        """Main backtesting loop."""
        log.info(f"Running backtest for {self.cfg.BACKTEST_DAYS} days...")

        # Import and create backtester to avoid circular imports
        from .backtester import Backtester
        backtester = Backtester(self.cfg, self.strategy)
        backtester.run()

    def _run_live(self):
        """Main live trading loop."""
        log.info("Running in live trading mode...")
        consecutive_errors = 0
        last_day = None

        # Initial position sync at startup
        if self.ensure_mt5_connection():
            self._sync_open_positions_count()
        else:
            log.critical("Cannot connect to MT5 at startup. Exiting.")
            return

        last_sync_time = time.time()

        while True:
            start_time = time.time()
            try:
                if not self.ensure_mt5_connection():
                    log.critical("Cannot reconnect to MT5. Closing positions and exiting.")
                    self.close_all_positions()
                    break

                current_time = self._get_current_time()

                # Sync positions periodically to prevent cache drift
                if time.time() - last_sync_time > 300:  # Sync every 5 minutes
                    self._sync_open_positions_count()
                    last_sync_time = time.time()

                should_continue, reason = self._should_continue_trading(current_time)
                if not should_continue:
                    log.info(f"Trading stopped: {reason}")
                    time.sleep(self.cfg.STRATEGY_INTERVAL)
                    continue

                current_day = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
                if last_day != current_day:
                    self._initialize_strategy()
                    last_day = current_day
                    log.info(f"Reinitialized strategy for {current_day.strftime('%Y-%m-%d')}")

                tick_data = self.broker_get_current_price(self.cfg.SYMBOL)
                if not tick_data or tick_data['ask'] <= 0 or tick_data['bid'] <= 0:
                    log.warning(f"Invalid tick data: {tick_data}")
                    time.sleep(1)
                    continue

                current_price = (tick_data['ask'] + tick_data['bid']) / 2

                # Update tick monitoring
                self._update_tick_monitoring(tick_data, current_time)
                self._log_tick_statistics(current_time)

                self._update_strategy(current_price, current_time)
                self._update_open_positions_profit()
                self._update_trade_history()

                signals = self.evaluate_entry_signals(current_price, current_time)

                if signals:
                    self._log_signal_events(signals, current_price)
                    for side, signal_data in signals:
                        position_id = self._get_next_available_position_slot()
                        if position_id:
                            # Use try/finally to ensure slot is released on any error
                            slot_allocated = False
                            try:
                                if self._allocate_position_slot(position_id):
                                    slot_allocated = True

                                    # Get position size from strategy
                                    strategy_lot_size = self.strategy.get_position_size(signal_data, self.trade_history)

                                    # Apply martingale if enabled (enhance, don't override)
                                    if getattr(self.cfg, 'ENABLE_MARTINGALE', False):
                                        _, martingale_lot_size, sequence_id = self._get_martingale_step_from_lot(strategy_lot_size, self.trade_history, position_id)
                                        current_lot = martingale_lot_size
                                        
                                        # Log martingale state for debugging
                                        martingale_state = self.get_martingale_state_for_position(position_id)
                                        log.info(f"Position {position_id}: Martingale state - {martingale_state['message']} | "
                                                f"Consecutive losses: {martingale_state['consecutive_losses']} | "
                                                f"Lot size: {current_lot:.2f} (base: {strategy_lot_size:.2f})")
                                    else:
                                        current_lot = strategy_lot_size
                                        sequence_id = self.current_martingale_sequence_id + 1
                                        self.current_martingale_sequence_id = sequence_id

                                    price = tick_data['ask'] if side == "Buy" else tick_data['bid']

                                    # Get SL/TP from strategy
                                    sl = self.strategy.get_stop_loss(side, price, signal_data)
                                    tp = self.strategy.get_take_profit(side, price, signal_data)

                                    trade = self.send_order(side, current_lot, sl, tp, price, sequence_id, position_id)
                                    if trade:
                                        self.trade_history.append(trade)
                                        self.strategy.on_trade_opened(trade)
                                        log.info(f"Opened position {position_id}/{self.cfg.MAX_OPEN_POSITIONS} | Open positions: {self.get_open_positions_count()}")
                                        slot_allocated = False  # Trade successful, don't release slot

                            finally:
                                # Release slot if trade failed or exception occurred
                                if slot_allocated:
                                    self._release_position_slot(position_id)

                consecutive_errors = 0

            except Exception as e:
                error_type = self._classify_error(e)
                log.error(f"Error in main loop ({error_type}): {e}")

                if error_type == "FATAL":
                    log.critical("Fatal error encountered. Closing positions and exiting.")
                    self.close_all_positions()
                    break
                elif error_type == "CONNECTION":
                    log.warning("Connection error. Will attempt to reconnect.")
                    consecutive_errors += 1
                elif error_type == "RECOVERABLE":
                    log.info("Recoverable error. Continuing after delay.")
                    consecutive_errors += 1
                else:
                    consecutive_errors += 1

                if consecutive_errors >= 5:
                    log.critical("Too many consecutive errors. Closing positions and exiting.")
                    self.close_all_positions()
                    break

                recovery_delay = self._get_error_recovery_delay(error_type)
                time.sleep(recovery_delay)
            
            elapsed_time = time.time() - start_time
            sleep_time = self.cfg.STRATEGY_INTERVAL - elapsed_time
            if sleep_time > 0:
                time.sleep(sleep_time)

    def _classify_error(self, error):
        """Classify error type for appropriate recovery strategy."""
        error_str = str(error).lower()
        error_type = type(error).__name__

        # Fatal errors that should stop trading immediately
        fatal_keywords = ['memory', 'disk', 'permission', 'access denied', 'configuration']
        if any(keyword in error_str for keyword in fatal_keywords):
            return "FATAL"

        # Connection-related errors
        connection_keywords = ['connection', 'network', 'timeout', 'socket', 'mt5', 'broker']
        if any(keyword in error_str for keyword in connection_keywords):
            return "CONNECTION"

        # Strategy-related errors (usually recoverable)
        if 'strategy' in error_str or error_type in ['AttributeError', 'KeyError']:
            return "STRATEGY"

        # Market data errors (recoverable)
        if 'tick' in error_str or 'price' in error_str or 'data' in error_str:
            return "DATA"

        # Default to recoverable
        return "RECOVERABLE"

    def _get_error_recovery_delay(self, error_type):
        """Get appropriate recovery delay based on error type."""
        delays = {
            "CONNECTION": 10,  # Longer delay for connection issues
            "STRATEGY": 2,     # Short delay for strategy issues
            "DATA": 1,         # Very short delay for data issues
            "RECOVERABLE": 5   # Default delay
        }
        return delays.get(error_type, getattr(self.cfg, "ERROR_RECOVERY_DELAY", 5))
