import logging
from datetime import datetime
from typing import List, Tuple, Dict, Any, Optional
from .base_strategy import BaseStrategy

log = logging.getLogger("ZoneBoS.BreakoutStrategy")

class BreakoutStrategy(BaseStrategy):
    """Zone-based breakout and reversal trading strategy.

    This strategy creates fixed-interval zones around a base price and trades
    breakouts through zones and reversals off zones. It includes sophisticated
    zone management with reversal detection, cooldowns, and martingale position sizing.
    """

    def __init__(self, config):
        super().__init__(config)
        self.zones = []
        self.last_zone_refresh_time = None
        self.confirmation_tracker = {"confirmations": {}}

    def get_strategy_config(self) -> Dict[str, Any]:
        """Return breakout strategy configuration."""
        return {
            # Trade Settings
            "BASE_LOT_SIZE": 0.01,
            "SL_POINTS": 6.5,    # Stop-loss in price points
            "TP_POINTS": 3.5,    # Take-profit in price points (1:1 risk-reward)

            # <PERSON><PERSON>e Settings
            "ENABLE_MARTINGALE": True,
            "MARTINGALE_MULT": 3.0,
            "MAX_MART_STEPS": 6,
            "MAX_SAFE_LOT_SIZE": 5.0, # Max lot size to prevent excessive risk

            # Multi-Position Settings
            "MAX_OPEN_POSITIONS": 3,  # Allow up to 3 positions simultaneously

            # Zone Settings
            "ZONE_GAP": 2.50,                    # Distance between zones (reduced for more coverage)
            "ZONE_REVERSAL_THRESHOLD": 3.25,    # Price distance to reverse a zone
            "ZONE_CONFIRMATION_CANDLES": 1,     # Immediate breakout confirmation (no delay)
            "NUM_ZONES_PER_SIDE": 400,          # Number of zones on each side of base price
            "ZONE_REFRESH_INTERVAL": 60,        # seconds to update zone classifications
            "ZONE_COOLDOWN_SECONDS": 300,       # 5 minutes cooldown before zone reactivation
            "TRADE_TYPE_COOLDOWN_SECONDS": 1800, # 30 minutes cooldown for same trade type on same zone
        }

    def validate_config(self, config_dict: Dict[str, Any]) -> None:
        """Validate breakout strategy configuration."""
        if config_dict.get('ZONE_GAP', 1) <= 0:
            raise ValueError("ZONE_GAP must be greater than 0")
        if config_dict.get('ZONE_REFRESH_INTERVAL', 1) <= 0:
            raise ValueError("ZONE_REFRESH_INTERVAL must be a positive number")
        if config_dict.get('MAX_SAFE_LOT_SIZE', 1) <= config_dict.get('BASE_LOT_SIZE', 0.01):
            raise ValueError("MAX_SAFE_LOT_SIZE must be greater than BASE_LOT_SIZE")
        if config_dict.get('ZONE_CONFIRMATION_CANDLES', 1) < 1:
            raise ValueError("ZONE_CONFIRMATION_CANDLES must be at least 1")
        if config_dict.get('MARTINGALE_MULT', 1) <= 1:
            raise ValueError("MARTINGALE_MULT must be greater than 1")
        if config_dict.get('MAX_MART_STEPS', 1) < 1:
            raise ValueError("MAX_MART_STEPS must be at least 1")
        if config_dict.get('SL_POINTS', 0) <= 0:
            raise ValueError("SL_POINTS must be greater than 0")
        if config_dict.get('TP_POINTS', 0) <= 0:
            raise ValueError("TP_POINTS must be greater than 0")
        if config_dict.get('NUM_ZONES_PER_SIDE', 1) < 10:
            raise ValueError("NUM_ZONES_PER_SIDE must be at least 10 for safety")

        self.logger.info("Breakout strategy configuration validated successfully")

    def initialize(self, current_price: float, historical_data=None) -> None:
        """Initialize the breakout strategy with zones."""
        self.logger.info("Initializing breakout strategy zones...")

        # Dynamically determine a stable base price from the current market price
        # Rounding to the nearest 100 creates a stable but relevant anchor
        base_generation_price = round(current_price / 100) * 100
        self.logger.info(f"Using dynamic base generation price for zones: {base_generation_price}")

        # Create fixed-interval zones from the dynamic base price
        self.zones = self._create_fixed_zones(
            current_price,
            self.config.ZONE_GAP,
            base_generation_price
        )

        # Validate that zones were created successfully
        if not self.zones or len(self.zones) == 0:
            self.logger.error("Zone creation failed - no zones were generated")
            raise RuntimeError("Zone initialization failed - no zones created")

        # Update zone classifications based on current price
        self.zones = self._update_zone_classification(self.zones, current_price)

        self.logger.info(f"Initialized {len(self.zones)} fixed-interval zones from base price {base_generation_price:.2f} around current price {current_price:.2f}")
        self.logger.info(f"Zone gap: {self.config.ZONE_GAP}")

        # Update refresh time
        if historical_data is not None and not historical_data.empty:
            self.last_zone_refresh_time = historical_data.index[-1]
        else:
            self.last_zone_refresh_time = datetime.now()

    def update(self, current_price: float, current_time: Optional[datetime] = None) -> None:
        """Update zone state with current market conditions."""
        if current_time is None:
            current_time = datetime.now()

        # 1. Check for zone reversals based on the latest price
        self.zones = self._check_zone_reversal(self.zones, current_price, self.config.ZONE_REVERSAL_THRESHOLD)

        # 2. Reactivate zones after cooldown period
        cooldown_seconds = getattr(self.config, 'ZONE_COOLDOWN_SECONDS', 300)
        self.zones = self._reactivate_zones_after_cooldown(self.zones, current_time, cooldown_seconds)

        # 3. Update zone classifications (support/resistance)
        # This should run periodically.
        refresh_interval = self.config.ZONE_REFRESH_INTERVAL
        if self.last_zone_refresh_time is None or (current_time.timestamp() - self.last_zone_refresh_time.timestamp()) >= refresh_interval:
            self.zones = self._update_zone_classification(self.zones, current_price)
            self.last_zone_refresh_time = current_time
            self.logger.debug(f"Updated zone classifications for current price: {current_price:.2f}")

    # --- Zone Management Methods ---

    def _create_fixed_zones(self, current_price, zone_gap, base_generation_price):
        """Create a large but finite number of fixed-interval zones around a base price.

        This ensures that zones remain consistent across different trading sessions
        and are not regenerated based on fluctuating current price.

        Args:
            current_price: Current market price (used for initial classification)
            zone_gap: Distance between zones
            base_generation_price: The stable price from which zones are generated

        Returns:
            List of zones with support/resistance classification
        """
        zones = []

        # Generate a large, but finite, number of zones for practical purposes
        num_zones_per_side = getattr(self.config, 'NUM_ZONES_PER_SIDE', 100)

        # Generate zones from the base price to ensure consistency
        for i in range(-num_zones_per_side, num_zones_per_side + 1):
            zone_price = base_generation_price + (i * zone_gap)

            zone = {
                'price': zone_price,
                'zone_type': 'support' if zone_price < current_price else 'resistance',
                'is_reversed': False,
                'last_break_time': None,
                'break_count': 0,
                'is_active': True,  # A zone is active and can be traded
                'deactivated_time': None,  # Track when zone was deactivated for cooldown
                'last_buy_trade_time': None,   # Track last buy trade time for cooldown
                'last_sell_trade_time': None   # Track last sell trade time for cooldown
            }
            zones.append(zone)

        zones.sort(key=lambda z: z['price'])
        self.logger.info(f"Created {len(zones)} fixed-interval zones from base price {base_generation_price:.2f}")
        return zones

    def _update_zone_classification(self, zones, current_price):
        """Update zone classification based on current price, respecting reversals.

        Args:
            zones: List of zones to update
            current_price: Current market price

        Returns:
            Updated zones with correct support/resistance classification
        """
        for zone in zones:
            # If a zone is reversed, its type is fixed until reversed back
            if zone.get('is_reversed', False):
                continue

            # Classify based on price relative to the zone
            if zone['price'] > current_price:
                zone['zone_type'] = 'resistance'
            else:
                zone['zone_type'] = 'support'
        return zones

    def _check_zone_reversal(self, zones, current_price, reversal_threshold):
        """Check if zones should be reversed or restored based on price movement.

        - A resistance zone becomes support if the price moves significantly above it.
        - A support zone becomes resistance if the price moves significantly below it.
        - A reversed zone can be restored to its original type if the price
          moves back across the original zone price.

        Args:
            zones: List of zones to check
            current_price: Current market price
            reversal_threshold: Distance price must move beyond a zone for reversal.

        Returns:
            Updated zones with reversal status.
        """
        for zone in zones:
            zone_price = zone['price']
            is_reversed = zone.get('is_reversed', False)

            if not is_reversed:
                # Logic to reverse a normal zone
                if zone['zone_type'] == 'resistance' and current_price > zone_price + reversal_threshold:
                    # Resistance flips to Support
                    zone['zone_type'] = 'support'
                    zone['is_reversed'] = True
                    zone['last_break_time'] = datetime.now()
                    zone['break_count'] += 1
                    zone['is_active'] = True # Reactivate the zone for new trades
                    self.logger.info(f"Resistance at {zone_price:.2f} REVERSED to Support at {datetime.now()}. Zone REACTIVATED.")

                elif zone['zone_type'] == 'support' and current_price < zone_price - reversal_threshold:
                    # Support flips to Resistance
                    zone['zone_type'] = 'resistance'
                    zone['is_reversed'] = True
                    zone['last_break_time'] = datetime.now()
                    zone['break_count'] += 1
                    zone['is_active'] = True # Reactivate the zone for new trades
                    self.logger.info(f"Support at {zone_price:.2f} REVERSED to Resistance at {datetime.now()}. Zone REACTIVATED.")

            else:
                # Logic to restore a reversed zone once price has moved
                # a full reversal_threshold in the opposite direction to avoid
                # immediate restoration that blocks breakout trades.

                if zone['zone_type'] == 'support' and current_price < zone_price - reversal_threshold:
                    # A reversed resistance (now support) restores to resistance
                    zone['zone_type'] = 'resistance'
                    zone['is_reversed'] = False
                    zone['is_active'] = True  # Reactivate the zone
                    self.logger.info(
                        f"Reversed zone at {zone_price:.2f} RESTORED to Resistance at {datetime.now()} "
                        f"after price moved {reversal_threshold:.2f} below the zone. Zone REACTIVATED."
                    )

                elif zone['zone_type'] == 'resistance' and current_price > zone_price + reversal_threshold:
                    # A reversed support (now resistance) restores to support
                    zone['zone_type'] = 'support'
                    zone['is_reversed'] = False
                    zone['is_active'] = True  # Reactivate the zone
                    self.logger.info(
                        f"Reversed zone at {zone_price:.2f} RESTORED to Support at {datetime.now()} "
                        f"after price moved {reversal_threshold:.2f} above the zone. Zone REACTIVATED."
                    )

        return zones

    def _reactivate_zones_after_cooldown(self, zones, current_time, cooldown_seconds):
        """Reactivate zones after cooldown period expires.

        Args:
            zones: List of zones to check
            current_time: Current datetime
            cooldown_seconds: Cooldown period in seconds

        Returns:
            Updated zones with reactivated zones
        """
        for zone in zones:
            if not zone.get('is_active', True) and zone.get('deactivated_time'):
                time_since_deactivation = (current_time - zone['deactivated_time']).total_seconds()
                if time_since_deactivation >= cooldown_seconds:
                    zone['is_active'] = True
                    zone['deactivated_time'] = None
                    self.logger.info(f"Zone at {zone['price']:.2f} REACTIVATED after {cooldown_seconds}s cooldown.")

        return zones

    def _is_trade_type_allowed(self, zone, trade_type, current_time, cooldown_seconds):
        """Check if a trade type is allowed on a zone based on cooldown period.

        Args:
            zone: Zone dictionary
            trade_type: 'Buy' or 'Sell'
            current_time: Current datetime
            cooldown_seconds: Cooldown period in seconds

        Returns:
            bool: True if trade type is allowed, False if still in cooldown
        """
        if trade_type == "Buy":
            last_trade_time = zone.get('last_buy_trade_time')
        else:  # Sell
            last_trade_time = zone.get('last_sell_trade_time')

        if last_trade_time is None:
            return True  # No previous trade of this type

        time_since_last_trade = (current_time - last_trade_time).total_seconds()
        return time_since_last_trade >= cooldown_seconds

    def _update_trade_type_timestamp(self, zone, trade_type, current_time):
        """Update the timestamp for the last trade of this type on this zone.

        Args:
            zone: Zone dictionary
            trade_type: 'Buy' or 'Sell'
            current_time: Current datetime
        """
        if trade_type == "Buy":
            zone['last_buy_trade_time'] = current_time
        else:  # Sell
            zone['last_sell_trade_time'] = current_time

    def get_entry_signals(self, tick_price: float, prev_tick_price: Optional[float] = None,
                         current_time: Optional[datetime] = None) -> List[Tuple[str, Dict[str, Any]]]:
        """Generate entry signals based on zone breakouts and reversals."""
        return self._evaluate_entry_signal_with_tick_price(tick_price, prev_tick_price, current_time)

    def get_strategy_state(self) -> Dict[str, Any]:
        """Get current strategy state for debugging/monitoring."""
        return {
            "strategy_name": self.__class__.__name__,
            "initialized": True,
            "zones_count": len(self.zones),
            "active_zones": len([z for z in self.zones if z.get('is_active', True)]),
            "last_zone_refresh": self.last_zone_refresh_time.isoformat() if self.last_zone_refresh_time else None
        }
    
    def get_display_info(self, current_price: float) -> str:
        """Get breakout strategy display information for live trading output."""
        zones_count = len(self.zones)
        active_zones = len([z for z in self.zones if z.get('is_active', True)])
        
        # Get nearest support and resistance levels
        nearest_support, nearest_resistance = self._get_nearest_zones(current_price)
        
        support_str = f"Support: {nearest_support:.2f}" if nearest_support else "Support: N/A"
        resistance_str = f"Resistance: {nearest_resistance:.2f}" if nearest_resistance else "Resistance: N/A"
        
        return (f"🎯 BREAKOUT STRATEGY | Price: {current_price:.2f} | "
                f"{support_str} | {resistance_str} | "
                f"Zones: {active_zones}/{zones_count} active")
    
    def get_signal_description(self, side: str, signal_data: Dict[str, Any], current_price: float) -> str:
        """Get custom signal description for breakout strategy."""
        signal_type = signal_data.get('signal_type', 'unknown')
        zone_info = signal_data.get('zone', {})
        zone_price = zone_info.get('price', 0)
        zone_type = signal_data.get('type', 'unknown')

        if signal_type == 'breakout':
            return (f"🚀 BREAKOUT SIGNAL | {side.upper()} | Price: {current_price:.2f} | "
                    f"Zone: {zone_price:.2f} ({zone_type}) | Action: Breaking through zone")
        else:
            return f"📊 BREAKOUT SIGNAL | {side.upper()} | Price: {current_price:.2f} | Type: {signal_type}"

    def on_trade_closed(self, trade_data: Dict[str, Any]) -> None:
        """Called when a trade is closed - print strategy information and open positions.

        Args:
            trade_data: Dictionary containing trade information including profit/loss
        """
        # Get current price from trade data or use a reasonable fallback
        current_price = trade_data.get('exit_price') or trade_data.get('entry_price', 0)

        # Print the strategy information when trade closes
        strategy_info = self.get_display_info(current_price)
        self.logger.info(strategy_info)

        # Print open positions count if available
        open_positions_count = trade_data.get('open_positions_count')
        if open_positions_count is not None:
            self.logger.info(f"Open positions: {open_positions_count}")

    def _get_nearest_zones(self, current_price):
        """Get nearest support and resistance zone levels."""
        try:
            active_zones = [z for z in self.zones if z.get('is_active', True)]
            
            # Find nearest support (below current price)
            support_zones = [z for z in active_zones if z['price'] < current_price]
            nearest_support = max(support_zones, key=lambda z: z['price'])['price'] if support_zones else None
            
            # Find nearest resistance (above current price)
            resistance_zones = [z for z in active_zones if z['price'] > current_price]
            nearest_resistance = min(resistance_zones, key=lambda z: z['price'])['price'] if resistance_zones else None
            
            return nearest_support, nearest_resistance
            
        except Exception as e:
            self.logger.debug(f"Error getting nearest zones: {e}")
            return None, None

    # --- Zone Analysis Methods ---

    def _evaluate_entry_signal_with_tick_price(self, tick_price, prev_tick_price=None, current_time=None):
        """Evaluate if there's an entry signal based on price breaking through a zone.

        This logic triggers entries for:
        1. Breakout trades: When price crosses through a zone
        
        Note: Zone type reversal (resistance becomes support, support becomes resistance) 
        is handled separately in the update() method when zones are broken.

        Args:
            tick_price: The current tick price.
            prev_tick_price: The previous tick price, to detect a cross.
            current_time: Current datetime for zone deactivation tracking.

        Returns:
            A list of tuples [(entry_signal, signal_data), ...], one for each signal.
        """
        if prev_tick_price is None:
            return []

        if self.confirmation_tracker is None:
            self.confirmation_tracker = {"confirmations": {}}

        confirmation_required = getattr(self.config, 'ZONE_CONFIRMATION_CANDLES', 1)
        trade_type_cooldown = getattr(self.config, 'TRADE_TYPE_COOLDOWN_SECONDS', 1800)
        signals = []

        for zone in self.zones:
            if not zone.get('is_active', True):
                continue

            zone_price = zone['price']
            zone_type = zone['zone_type']

            # 1. BREAKOUT SIGNALS: Price crossing through the zone
            crossed_up = prev_tick_price <= zone_price < tick_price
            crossed_down = prev_tick_price >= zone_price > tick_price

            breakout_signal = None
            if zone_type == 'resistance' and crossed_up:
                breakout_signal = "Buy"
            elif zone_type == 'support' and crossed_down:
                breakout_signal = "Sell"

            # Process breakout signals only
            entry_signal = breakout_signal
            signal_type = "breakout"

            if entry_signal:
                # Check trade type cooldown
                if not self._is_trade_type_allowed(zone, entry_signal, current_time or datetime.now(), trade_type_cooldown):
                    self.logger.debug(f"Zone at {zone_price:.2f} {entry_signal} signal blocked - trade type cooldown active")
                    continue

                # Check confirmation
                zone_key = f"{zone_type}_{zone_price:.2f}_{signal_type}"

                if confirmation_required <= 1:
                    # Immediate signal
                    pass
                else:
                    # Start confirmation tracking
                    self.confirmation_tracker["confirmations"][zone_key] = self.confirmation_tracker["confirmations"].get(zone_key, 0) + 1
                    if self.confirmation_tracker["confirmations"][zone_key] < confirmation_required:
                        continue  # Not enough confirmation yet
                    self.confirmation_tracker["confirmations"][zone_key] = 0  # Reset after triggering

                # Create signal
                signal_data = {"type": zone_type, "zone": zone, "signal_type": signal_type}
                signals.append((entry_signal, signal_data))

                # Update trade type timestamp
                self._update_trade_type_timestamp(zone, entry_signal, current_time or datetime.now())

                # For breakouts, deactivate the zone completely
                zone['is_active'] = False
                zone['deactivated_time'] = current_time or datetime.now()
                self.logger.info(f"Zone at {zone['price']:.2f} DEACTIVATED after {signal_type} signal for a {entry_signal}.")

        # Clean up stale confirmations
        active_keys = {f"{z['zone_type']}_{z['price']:.2f}" for z in self.zones}
        stale_keys = [k for k in self.confirmation_tracker["confirmations"] if k not in active_keys]
        for k in stale_keys:
            del self.confirmation_tracker["confirmations"][k]

        return signals


# --- Backward Compatibility Functions ---
# These functions maintain compatibility with the old function-based interface
# They will be removed in a future version

def get_strategy_instance(config):
    """Factory function to create a BreakoutStrategy instance."""
    return BreakoutStrategy(config)

# Legacy function exports for backward compatibility
def create_strategy_config():
    """Get strategy configuration for backward compatibility."""
    temp_strategy = BreakoutStrategy(None)
    return temp_strategy.get_strategy_config()

strategy_config = create_strategy_config()

def validate_strategy_config(config_dict):
    """Legacy validation function."""
    temp_strategy = BreakoutStrategy(None)
    return temp_strategy.validate_config(config_dict)